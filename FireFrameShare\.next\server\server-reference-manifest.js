self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"40b11c9b700e2ac87cc1ab9113cdcba1a31b2bf8f4\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/src/ai/flows/get-suggested-users-flow.ts [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"action-browser\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"8aMa/nWEyoa5Yi7yJ5AluEX+Dpu4412s51W70JNz/74=\"\n}"
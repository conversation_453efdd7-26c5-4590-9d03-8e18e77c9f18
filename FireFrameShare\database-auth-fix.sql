-- =====================================================
-- SUPABASE AUTH USER PROFILE CREATION FIX
-- =====================================================
-- This SQL fixes the issue where users exist in auth.users 
-- but don't have corresponding records in public.users
-- 
-- Run this in your Supabase SQL Editor to fix the authentication issues
-- =====================================================

-- 1. Create function to handle new user creation in public.users table
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, username, email, created_at, updated_at)
  VALUES (
    NEW.id,
    COALESCE(
      NEW.raw_user_meta_data->>'username',
      NEW.user_metadata->>'username', 
      split_part(NEW.email, '@', 1)
    ),
    NEW.email,
    NOW(),
    NOW()
  )
  ON CONFLICT (id) DO NOTHING;
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- 2. Create trigger to automatically create user profile on signup
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 3. Create user records for any existing auth users who don't have profiles
-- This handles users who signed up before the trigger was in place
INSERT INTO public.users (id, username, email, created_at, updated_at)
SELECT 
  au.id,
  COALESCE(
    au.raw_user_meta_data->>'username',
    au.user_metadata->>'username',
    split_part(au.email, '@', 1)
  ) as username,
  au.email,
  au.created_at,
  au.updated_at
FROM auth.users au
WHERE au.id NOT IN (SELECT id FROM public.users)
  AND au.email IS NOT NULL
ON CONFLICT (id) DO NOTHING;

-- 4. Verify the setup worked
-- Check if all auth users now have corresponding user profiles
SELECT 
  'Auth users without profiles' as check_type,
  COUNT(*) as count
FROM auth.users au
LEFT JOIN public.users pu ON au.id = pu.id
WHERE pu.id IS NULL;

-- 5. Show recent user creations to verify
SELECT 
  'Recent user profiles' as info,
  id,
  username,
  email,
  created_at
FROM public.users
ORDER BY created_at DESC
LIMIT 5;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================
-- Run these to verify everything is working:

-- Check trigger exists:
-- SELECT * FROM information_schema.triggers WHERE trigger_name = 'on_auth_user_created';

-- Check function exists:
-- SELECT * FROM information_schema.routines WHERE routine_name = 'handle_new_user';

-- Check user count matches:
-- SELECT 
--   (SELECT COUNT(*) FROM auth.users) as auth_users,
--   (SELECT COUNT(*) FROM public.users) as public_users;

{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst buttonVariants = cva(\r\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg]:size-4 [&_svg]:shrink-0\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\r\n        destructive:\r\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\r\n        outline:\r\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\r\n        secondary:\r\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\r\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\r\n        link: \"text-primary underline-offset-4 hover:underline\",\r\n      },\r\n      size: {\r\n        default: \"h-10 px-4 py-2\",\r\n        sm: \"h-9 rounded-md px-3\",\r\n        lg: \"h-11 rounded-md px-8\",\r\n        icon: \"h-10 w-10\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n      size: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nexport interface ButtonProps\r\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\r\n    VariantProps<typeof buttonVariants> {\r\n  asChild?: boolean\r\n}\r\n\r\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\r\n  ({ className, variant, size, asChild = false, ...props }, ref) => {\r\n    const Comp = asChild ? Slot : \"button\"\r\n    return (\r\n      <Comp\r\n        className={cn(buttonVariants({ variant, size, className }))}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nButton.displayName = \"Button\"\r\n\r\nexport { Button, buttonVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,4VACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AASF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,GAAG,OAAO,EAAE;IACxD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAC9B,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Card = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"rounded-lg border bg-card text-card-foreground shadow-sm\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCard.displayName = \"Card\"\r\n\r\nconst CardHeader = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex flex-col space-y-1.5 p-6\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardHeader.displayName = \"CardHeader\"\r\n\r\nconst CardTitle = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\r\n      \"text-2xl font-semibold leading-none tracking-tight\",\r\n      className\r\n    )}\r\n    {...props}\r\n  />\r\n))\r\nCardTitle.displayName = \"CardTitle\"\r\n\r\nconst CardDescription = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm text-muted-foreground\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardDescription.displayName = \"CardDescription\"\r\n\r\nconst CardContent = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div ref={ref} className={cn(\"p-6 pt-0\", className)} {...props} />\r\n))\r\nCardContent.displayName = \"CardContent\"\r\n\r\nconst CardFooter = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"flex items-center p-6 pt-0\", className)}\r\n    {...props}\r\n  />\r\n))\r\nCardFooter.displayName = \"CardFooter\"\r\n\r\nexport { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent }\r\n"], "names": [], "mappings": ";;;;;;;;;AAAA;AAEA;;;;AAEA,MAAM,qBAAO,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG1B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,4DACA;QAED,GAAG,KAAK;;;;;;AAGb,KAAK,WAAW,GAAG;AAEnB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sDACA;QAED,GAAG,KAAK;;;;;;AAGb,UAAU,WAAW,GAAG;AAExB,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QAAI,KAAK;QAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,YAAY;QAAa,GAAG,KAAK;;;;;;AAEhE,YAAY,WAAW,GAAG;AAE1B,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 172, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/input.tsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Input = React.forwardRef<HTMLInputElement, React.ComponentProps<\"input\">>(\r\n  ({ className, type, ...props }, ref) => {\r\n    return (\r\n      <input\r\n        type={type}\r\n        className={cn(\r\n          \"flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-base ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 md:text-sm\",\r\n          className\r\n        )}\r\n        ref={ref}\r\n        {...props}\r\n      />\r\n    )\r\n  }\r\n)\r\nInput.displayName = \"Input\"\r\n\r\nexport { Input }\r\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAEA,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC3B,CAAC,EAAE,SAAS,EAAE,IAAI,EAAE,GAAG,OAAO,EAAE;IAC9B,qBACE,8OAAC;QACC,MAAM;QACN,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kYACA;QAEF,KAAK;QACJ,GAAG,KAAK;;;;;;AAGf;AAEF,MAAM,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 201, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/label.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst labelVariants = cva(\r\n  \"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70\"\r\n)\r\n\r\nconst Label = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root> &\r\n    VariantProps<typeof labelVariants>\r\n>(({ className, ...props }, ref) => (\r\n  <LabelPrimitive.Root\r\n    ref={ref}\r\n    className={cn(labelVariants(), className)}\r\n    {...props}\r\n  />\r\n))\r\nLabel.displayName = LabelPrimitive.Root.displayName\r\n\r\nexport { Label }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAI3B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,iKAAA,CAAA,OAAmB;QAClB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;QAC9B,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG,iKAAA,CAAA,OAAmB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 233, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/hooks/use-auth.ts"], "sourcesContent": ["\"use client\";\r\n\r\nimport { create } from \"zustand\";\r\nimport { persist, createJSONStorage } from \"zustand/middleware\";\r\nimport { User } from \"@/lib/types\";\r\nimport { supabase } from \"@/lib/supabase\";\r\nimport {\r\n  AuthError,\r\n  Session,\r\n  User as SupabaseUser,\r\n} from \"@supabase/supabase-js\";\r\nimport { useEffect } from \"react\";\r\n\r\ninterface AuthState {\r\n  user: User | null;\r\n  session: Session | null;\r\n  isAuthenticated: boolean;\r\n  isLoading: boolean;\r\n  error: string | null;\r\n  // Auth methods\r\n  signUp: (\r\n    email: string,\r\n    password: string,\r\n    username: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signIn: (\r\n    email: string,\r\n    password: string\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signInWithOAuth: (\r\n    provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n  ) => Promise<{ error: AuthError | null }>;\r\n  signOut: () => Promise<void>;\r\n  resetPassword: (email: string) => Promise<{ error: AuthError | null }>;\r\n  updateProfile: (updates: Partial<User>) => Promise<{ error: Error | null }>;\r\n  uploadAvatar: (\r\n    file: File\r\n  ) => Promise<{ url: string | null; error: Error | null }>;\r\n  // Internal methods\r\n  setSession: (session: Session | null) => void;\r\n  setUser: (user: User | null) => void;\r\n  setLoading: (loading: boolean) => void;\r\n  setError: (error: string | null) => void;\r\n}\r\n\r\nconst useAuthStore = create<AuthState>()(\r\n  persist(\r\n    (set, get) => ({\r\n      user: null,\r\n      session: null,\r\n      isAuthenticated: false,\r\n      isLoading: true,\r\n      error: null,\r\n\r\n      signUp: async (email: string, password: string, username: string) => {\r\n        console.log(\"🚀 Starting signup process...\");\r\n        console.log(\"- Email:\", email);\r\n        console.log(\"- Username:\", username);\r\n        console.log(\"- Supabase client:\", !!supabase);\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        try {\r\n          const { data, error } = await supabase.auth.signUp({\r\n            email,\r\n            password,\r\n            options: {\r\n              data: {\r\n                username,\r\n              },\r\n            },\r\n          });\r\n\r\n          console.log(\"📝 Signup response:\", { data, error });\r\n\r\n          if (error) {\r\n            console.error(\"❌ Signup error:\", error);\r\n            set({ error: error.message, isLoading: false });\r\n            return { error };\r\n          }\r\n\r\n          console.log(\"✅ Signup successful!\");\r\n          // User will be created in the database via trigger\r\n          set({ isLoading: false });\r\n          return { error: null };\r\n        } catch (err) {\r\n          console.error(\"💥 Signup exception:\", err);\r\n          const errorMessage =\r\n            err instanceof Error ? err.message : \"Unknown error\";\r\n          set({ error: errorMessage, isLoading: false });\r\n          return { error: { message: errorMessage } as any };\r\n        }\r\n      },\r\n\r\n      signIn: async (email: string, password: string) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithPassword({\r\n          email,\r\n          password,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        // Session will be handled by the auth state change listener\r\n        return { error: null };\r\n      },\r\n\r\n      signInWithOAuth: async (\r\n        provider: \"google\" | \"azure\" | \"discord\" | \"facebook\"\r\n      ) => {\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { data, error } = await supabase.auth.signInWithOAuth({\r\n          provider,\r\n          options: {\r\n            redirectTo: `${window.location.origin}/auth/callback`,\r\n          },\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      signOut: async () => {\r\n        set({ isLoading: true });\r\n        await supabase.auth.signOut();\r\n        set({\r\n          user: null,\r\n          session: null,\r\n          isAuthenticated: false,\r\n          isLoading: false,\r\n          error: null,\r\n        });\r\n      },\r\n\r\n      resetPassword: async (email: string) => {\r\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\r\n          redirectTo: `${window.location.origin}/auth/reset-password`,\r\n        });\r\n\r\n        if (error) {\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      updateProfile: async (updates: Partial<User>) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          set({ error: error.message });\r\n          return { error };\r\n        }\r\n\r\n        set({ isLoading: true, error: null });\r\n\r\n        const { error } = await supabase\r\n          .from(\"users\")\r\n          .update(updates)\r\n          .eq(\"id\", user.id);\r\n\r\n        if (error) {\r\n          set({ error: error.message, isLoading: false });\r\n          return { error: new Error(error.message) };\r\n        }\r\n\r\n        // Update local user state\r\n        set({\r\n          user: { ...user, ...updates },\r\n          isLoading: false,\r\n        });\r\n\r\n        return { error: null };\r\n      },\r\n\r\n      uploadAvatar: async (file: File) => {\r\n        const { user } = get();\r\n        if (!user) {\r\n          const error = new Error(\"No user logged in\");\r\n          return { url: null, error };\r\n        }\r\n\r\n        const fileExt = file.name.split(\".\").pop();\r\n        const fileName = `${user.id}/avatar.${fileExt}`;\r\n\r\n        const { error: uploadError } = await supabase.storage\r\n          .from(\"avatars\")\r\n          .upload(fileName, file, { upsert: true });\r\n\r\n        if (uploadError) {\r\n          return { url: null, error: new Error(uploadError.message) };\r\n        }\r\n\r\n        const { data } = supabase.storage\r\n          .from(\"avatars\")\r\n          .getPublicUrl(fileName);\r\n\r\n        const avatarUrl = data.publicUrl;\r\n\r\n        // Update user profile with new avatar URL\r\n        const { error: updateError } = await get().updateProfile({ avatarUrl });\r\n\r\n        if (updateError) {\r\n          return { url: null, error: updateError };\r\n        }\r\n\r\n        return { url: avatarUrl, error: null };\r\n      },\r\n\r\n      setSession: (session: Session | null) => {\r\n        set({ session, isAuthenticated: !!session });\r\n      },\r\n\r\n      setUser: (user: User | null) => {\r\n        set({ user, isAuthenticated: !!user, isLoading: false });\r\n      },\r\n\r\n      setLoading: (loading: boolean) => {\r\n        set({ isLoading: loading });\r\n      },\r\n\r\n      setError: (error: string | null) => {\r\n        set({ error });\r\n      },\r\n    }),\r\n    {\r\n      name: \"auth-storage\",\r\n      storage: createJSONStorage(() => localStorage),\r\n      partialize: (state) => ({\r\n        // Only persist user data, not session (Supabase handles session persistence)\r\n        user: state.user,\r\n      }),\r\n    }\r\n  )\r\n);\r\n\r\n// Helper function to fetch user profile from database\r\nconst fetchUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  try {\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .select(\"*\")\r\n      .eq(\"id\", supabaseUser.id)\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"Error fetching user profile:\", error);\r\n\r\n      // If user doesn't exist in users table, try to create one\r\n      if (error.code === \"PGRST116\") {\r\n        console.log(\r\n          \"🔄 User not found in users table, attempting to create...\"\r\n        );\r\n        return await createUserProfile(supabaseUser);\r\n      }\r\n\r\n      return null;\r\n    }\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"Exception in fetchUserProfile:\", err);\r\n    return null;\r\n  }\r\n};\r\n\r\n// Helper function to create a user profile when one doesn't exist\r\nconst createUserProfile = async (\r\n  supabaseUser: SupabaseUser\r\n): Promise<User | null> => {\r\n  try {\r\n    const username =\r\n      supabaseUser.user_metadata?.username ||\r\n      supabaseUser.email?.split(\"@\")[0] ||\r\n      \"user\";\r\n\r\n    console.log(\"📝 Creating user profile for:\", {\r\n      id: supabaseUser.id,\r\n      email: supabaseUser.email,\r\n      username: username,\r\n    });\r\n\r\n    const { data, error } = await supabase\r\n      .from(\"users\")\r\n      .insert({\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email,\r\n      })\r\n      .select()\r\n      .single();\r\n\r\n    if (error) {\r\n      console.error(\"❌ Failed to create user profile:\", error);\r\n\r\n      // Return a minimal user object to prevent app crashes\r\n      return {\r\n        id: supabaseUser.id,\r\n        username: username,\r\n        email: supabaseUser.email || \"\",\r\n        contacts: {\r\n          website: { value: \"\", isPublic: false },\r\n          phone: { value: \"\", isPublic: false },\r\n          messaging: { platform: \"\", username: \"\", isPublic: false },\r\n        },\r\n      };\r\n    }\r\n\r\n    console.log(\"✅ User profile created successfully:\", data);\r\n\r\n    return {\r\n      id: data.id,\r\n      username: data.username,\r\n      email: data.email,\r\n      avatarUrl: data.avatar_url,\r\n      bio: data.bio,\r\n      contacts: {\r\n        website: {\r\n          value: data.website_url || \"\",\r\n          isPublic: data.website_public || false,\r\n        },\r\n        phone: {\r\n          value: data.phone || \"\",\r\n          isPublic: data.phone_public || false,\r\n        },\r\n        messaging: {\r\n          platform: data.messaging_platform || \"\",\r\n          username: data.messaging_username || \"\",\r\n          isPublic: data.messaging_public || false,\r\n        },\r\n      },\r\n    };\r\n  } catch (err) {\r\n    console.error(\"💥 Exception in createUserProfile:\", err);\r\n\r\n    // Return a minimal user object as last resort\r\n    return {\r\n      id: supabaseUser.id,\r\n      username:\r\n        supabaseUser.user_metadata?.username ||\r\n        supabaseUser.email?.split(\"@\")[0] ||\r\n        \"user\",\r\n      email: supabaseUser.email || \"\",\r\n      contacts: {\r\n        website: { value: \"\", isPublic: false },\r\n        phone: { value: \"\", isPublic: false },\r\n        messaging: { platform: \"\", username: \"\", isPublic: false },\r\n      },\r\n    };\r\n  }\r\n};\r\n\r\n// Custom hook to initialize and use the store\r\nexport const useAuth = () => {\r\n  const state = useAuthStore();\r\n\r\n  useEffect(() => {\r\n    // Initialize auth state\r\n    const initializeAuth = async () => {\r\n      const {\r\n        data: { session },\r\n      } = await supabase.auth.getSession();\r\n\r\n      if (session?.user) {\r\n        const userProfile = await fetchUserProfile(session.user);\r\n        useAuthStore.getState().setSession(session);\r\n        useAuthStore.getState().setUser(userProfile);\r\n      } else {\r\n        useAuthStore.getState().setLoading(false);\r\n      }\r\n    };\r\n\r\n    // Listen for auth changes\r\n    const {\r\n      data: { subscription },\r\n    } = supabase.auth.onAuthStateChange(async (event, session) => {\r\n      console.log(\"Auth state changed:\", event, session);\r\n\r\n      if (session?.user) {\r\n        const userProfile = await fetchUserProfile(session.user);\r\n        useAuthStore.getState().setSession(session);\r\n        useAuthStore.getState().setUser(userProfile);\r\n      } else {\r\n        useAuthStore.getState().setSession(null);\r\n        useAuthStore.getState().setUser(null);\r\n      }\r\n    });\r\n\r\n    initializeAuth();\r\n\r\n    return () => {\r\n      subscription.unsubscribe();\r\n    };\r\n  }, []);\r\n\r\n  return state;\r\n};\r\n"], "names": [], "mappings": ";;;AAEA;AACA;AAEA;AAMA;AAXA;;;;;AA6CA,MAAM,eAAe,CAAA,GAAA,wJAAA,CAAA,SAAM,AAAD,IACxB,CAAA,GAAA,6IAAA,CAAA,UAAO,AAAD,EACJ,CAAC,KAAK,MAAQ,CAAC;QACb,MAAM;QACN,SAAS;QACT,iBAAiB;QACjB,WAAW;QACX,OAAO;QAEP,QAAQ,OAAO,OAAe,UAAkB;YAC9C,QAAQ,GAAG,CAAC;YACZ,QAAQ,GAAG,CAAC,YAAY;YACxB,QAAQ,GAAG,CAAC,eAAe;YAC3B,QAAQ,GAAG,CAAC,sBAAsB,CAAC,CAAC,sHAAA,CAAA,WAAQ;YAE5C,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,IAAI;gBACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,MAAM,CAAC;oBACjD;oBACA;oBACA,SAAS;wBACP,MAAM;4BACJ;wBACF;oBACF;gBACF;gBAEA,QAAQ,GAAG,CAAC,uBAAuB;oBAAE;oBAAM;gBAAM;gBAEjD,IAAI,OAAO;oBACT,QAAQ,KAAK,CAAC,mBAAmB;oBACjC,IAAI;wBAAE,OAAO,MAAM,OAAO;wBAAE,WAAW;oBAAM;oBAC7C,OAAO;wBAAE;oBAAM;gBACjB;gBAEA,QAAQ,GAAG,CAAC;gBACZ,mDAAmD;gBACnD,IAAI;oBAAE,WAAW;gBAAM;gBACvB,OAAO;oBAAE,OAAO;gBAAK;YACvB,EAAE,OAAO,KAAK;gBACZ,QAAQ,KAAK,CAAC,wBAAwB;gBACtC,MAAM,eACJ,eAAe,QAAQ,IAAI,OAAO,GAAG;gBACvC,IAAI;oBAAE,OAAO;oBAAc,WAAW;gBAAM;gBAC5C,OAAO;oBAAE,OAAO;wBAAE,SAAS;oBAAa;gBAAS;YACnD;QACF;QAEA,QAAQ,OAAO,OAAe;YAC5B,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,kBAAkB,CAAC;gBAC7D;gBACA;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,4DAA4D;YAC5D,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,iBAAiB,OACf;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,eAAe,CAAC;gBAC1D;gBACA,SAAS;oBACP,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,cAAc,CAAC;gBACvD;YACF;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,SAAS;YACP,IAAI;gBAAE,WAAW;YAAK;YACtB,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,OAAO;YAC3B,IAAI;gBACF,MAAM;gBACN,SAAS;gBACT,iBAAiB;gBACjB,WAAW;gBACX,OAAO;YACT;QACF;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,qBAAqB,CAAC,OAAO;gBACjE,YAAY,GAAG,OAAO,QAAQ,CAAC,MAAM,CAAC,oBAAoB,CAAC;YAC7D;YAEA,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,eAAe,OAAO;YACpB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,IAAI;oBAAE,OAAO,MAAM,OAAO;gBAAC;gBAC3B,OAAO;oBAAE;gBAAM;YACjB;YAEA,IAAI;gBAAE,WAAW;gBAAM,OAAO;YAAK;YAEnC,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAC7B,IAAI,CAAC,SACL,MAAM,CAAC,SACP,EAAE,CAAC,MAAM,KAAK,EAAE;YAEnB,IAAI,OAAO;gBACT,IAAI;oBAAE,OAAO,MAAM,OAAO;oBAAE,WAAW;gBAAM;gBAC7C,OAAO;oBAAE,OAAO,IAAI,MAAM,MAAM,OAAO;gBAAE;YAC3C;YAEA,0BAA0B;YAC1B,IAAI;gBACF,MAAM;oBAAE,GAAG,IAAI;oBAAE,GAAG,OAAO;gBAAC;gBAC5B,WAAW;YACb;YAEA,OAAO;gBAAE,OAAO;YAAK;QACvB;QAEA,cAAc,OAAO;YACnB,MAAM,EAAE,IAAI,EAAE,GAAG;YACjB,IAAI,CAAC,MAAM;gBACT,MAAM,QAAQ,IAAI,MAAM;gBACxB,OAAO;oBAAE,KAAK;oBAAM;gBAAM;YAC5B;YAEA,MAAM,UAAU,KAAK,IAAI,CAAC,KAAK,CAAC,KAAK,GAAG;YACxC,MAAM,WAAW,GAAG,KAAK,EAAE,CAAC,QAAQ,EAAE,SAAS;YAE/C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAClD,IAAI,CAAC,WACL,MAAM,CAAC,UAAU,MAAM;gBAAE,QAAQ;YAAK;YAEzC,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO,IAAI,MAAM,YAAY,OAAO;gBAAE;YAC5D;YAEA,MAAM,EAAE,IAAI,EAAE,GAAG,sHAAA,CAAA,WAAQ,CAAC,OAAO,CAC9B,IAAI,CAAC,WACL,YAAY,CAAC;YAEhB,MAAM,YAAY,KAAK,SAAS;YAEhC,0CAA0C;YAC1C,MAAM,EAAE,OAAO,WAAW,EAAE,GAAG,MAAM,MAAM,aAAa,CAAC;gBAAE;YAAU;YAErE,IAAI,aAAa;gBACf,OAAO;oBAAE,KAAK;oBAAM,OAAO;gBAAY;YACzC;YAEA,OAAO;gBAAE,KAAK;gBAAW,OAAO;YAAK;QACvC;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE;gBAAS,iBAAiB,CAAC,CAAC;YAAQ;QAC5C;QAEA,SAAS,CAAC;YACR,IAAI;gBAAE;gBAAM,iBAAiB,CAAC,CAAC;gBAAM,WAAW;YAAM;QACxD;QAEA,YAAY,CAAC;YACX,IAAI;gBAAE,WAAW;YAAQ;QAC3B;QAEA,UAAU,CAAC;YACT,IAAI;gBAAE;YAAM;QACd;IACF,CAAC,GACD;IACE,MAAM;IACN,SAAS,CAAA,GAAA,6IAAA,CAAA,oBAAiB,AAAD,EAAE,IAAM;IACjC,YAAY,CAAC,QAAU,CAAC;YACtB,6EAA6E;YAC7E,MAAM,MAAM,IAAI;QAClB,CAAC;AACH;AAIJ,sDAAsD;AACtD,MAAM,mBAAmB,OACvB;IAEA,IAAI;QACF,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC,KACP,EAAE,CAAC,MAAM,aAAa,EAAE,EACxB,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,gCAAgC;YAE9C,0DAA0D;YAC1D,IAAI,MAAM,IAAI,KAAK,YAAY;gBAC7B,QAAQ,GAAG,CACT;gBAEF,OAAO,MAAM,kBAAkB;YACjC;YAEA,OAAO;QACT;QAEA,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,kCAAkC;QAChD,OAAO;IACT;AACF;AAEA,kEAAkE;AAClE,MAAM,oBAAoB,OACxB;IAEA,IAAI;QACF,MAAM,WACJ,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;QAEF,QAAQ,GAAG,CAAC,iCAAiC;YAC3C,IAAI,aAAa,EAAE;YACnB,OAAO,aAAa,KAAK;YACzB,UAAU;QACZ;QAEA,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,MAAM,sHAAA,CAAA,WAAQ,CACnC,IAAI,CAAC,SACL,MAAM,CAAC;YACN,IAAI,aAAa,EAAE;YACnB,UAAU;YACV,OAAO,aAAa,KAAK;QAC3B,GACC,MAAM,GACN,MAAM;QAET,IAAI,OAAO;YACT,QAAQ,KAAK,CAAC,oCAAoC;YAElD,sDAAsD;YACtD,OAAO;gBACL,IAAI,aAAa,EAAE;gBACnB,UAAU;gBACV,OAAO,aAAa,KAAK,IAAI;gBAC7B,UAAU;oBACR,SAAS;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACtC,OAAO;wBAAE,OAAO;wBAAI,UAAU;oBAAM;oBACpC,WAAW;wBAAE,UAAU;wBAAI,UAAU;wBAAI,UAAU;oBAAM;gBAC3D;YACF;QACF;QAEA,QAAQ,GAAG,CAAC,wCAAwC;QAEpD,OAAO;YACL,IAAI,KAAK,EAAE;YACX,UAAU,KAAK,QAAQ;YACvB,OAAO,KAAK,KAAK;YACjB,WAAW,KAAK,UAAU;YAC1B,KAAK,KAAK,GAAG;YACb,UAAU;gBACR,SAAS;oBACP,OAAO,KAAK,WAAW,IAAI;oBAC3B,UAAU,KAAK,cAAc,IAAI;gBACnC;gBACA,OAAO;oBACL,OAAO,KAAK,KAAK,IAAI;oBACrB,UAAU,KAAK,YAAY,IAAI;gBACjC;gBACA,WAAW;oBACT,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,kBAAkB,IAAI;oBACrC,UAAU,KAAK,gBAAgB,IAAI;gBACrC;YACF;QACF;IACF,EAAE,OAAO,KAAK;QACZ,QAAQ,KAAK,CAAC,sCAAsC;QAEpD,8CAA8C;QAC9C,OAAO;YACL,IAAI,aAAa,EAAE;YACnB,UACE,aAAa,aAAa,EAAE,YAC5B,aAAa,KAAK,EAAE,MAAM,IAAI,CAAC,EAAE,IACjC;YACF,OAAO,aAAa,KAAK,IAAI;YAC7B,UAAU;gBACR,SAAS;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACtC,OAAO;oBAAE,OAAO;oBAAI,UAAU;gBAAM;gBACpC,WAAW;oBAAE,UAAU;oBAAI,UAAU;oBAAI,UAAU;gBAAM;YAC3D;QACF;IACF;AACF;AAGO,MAAM,UAAU;IACrB,MAAM,QAAQ;IAEd,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,wBAAwB;QACxB,MAAM,iBAAiB;YACrB,MAAM,EACJ,MAAM,EAAE,OAAO,EAAE,EAClB,GAAG,MAAM,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,UAAU;YAElC,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;gBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC,OAAO;gBACL,aAAa,QAAQ,GAAG,UAAU,CAAC;YACrC;QACF;QAEA,0BAA0B;QAC1B,MAAM,EACJ,MAAM,EAAE,YAAY,EAAE,EACvB,GAAG,sHAAA,CAAA,WAAQ,CAAC,IAAI,CAAC,iBAAiB,CAAC,OAAO,OAAO;YAChD,QAAQ,GAAG,CAAC,uBAAuB,OAAO;YAE1C,IAAI,SAAS,MAAM;gBACjB,MAAM,cAAc,MAAM,iBAAiB,QAAQ,IAAI;gBACvD,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC,OAAO;gBACL,aAAa,QAAQ,GAAG,UAAU,CAAC;gBACnC,aAAa,QAAQ,GAAG,OAAO,CAAC;YAClC;QACF;QAEA;QAEA,OAAO;YACL,aAAa,WAAW;QAC1B;IACF,GAAG,EAAE;IAEL,OAAO;AACT", "debugId": null}}, {"offset": {"line": 652, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/form.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as LabelPrimitive from \"@radix-ui/react-label\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport {\r\n  Controller,\r\n  FormProvider,\r\n  useFormContext,\r\n  type ControllerProps,\r\n  type FieldPath,\r\n  type FieldValues,\r\n} from \"react-hook-form\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\nimport { Label } from \"@/components/ui/label\"\r\n\r\nconst Form = FormProvider\r\n\r\ntype FormFieldContextValue<\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n> = {\r\n  name: TName\r\n}\r\n\r\nconst FormFieldContext = React.createContext<FormFieldContextValue>(\r\n  {} as FormFieldContextValue\r\n)\r\n\r\nconst FormField = <\r\n  TFieldValues extends FieldValues = FieldValues,\r\n  TName extends FieldPath<TFieldValues> = FieldPath<TFieldValues>\r\n>({\r\n  ...props\r\n}: ControllerProps<TFieldValues, TName>) => {\r\n  return (\r\n    <FormFieldContext.Provider value={{ name: props.name }}>\r\n      <Controller {...props} />\r\n    </FormFieldContext.Provider>\r\n  )\r\n}\r\n\r\nconst useFormField = () => {\r\n  const fieldContext = React.useContext(FormFieldContext)\r\n  const itemContext = React.useContext(FormItemContext)\r\n  const { getFieldState, formState } = useFormContext()\r\n\r\n  const fieldState = getFieldState(fieldContext.name, formState)\r\n\r\n  if (!fieldContext) {\r\n    throw new Error(\"useFormField should be used within <FormField>\")\r\n  }\r\n\r\n  const { id } = itemContext\r\n\r\n  return {\r\n    id,\r\n    name: fieldContext.name,\r\n    formItemId: `${id}-form-item`,\r\n    formDescriptionId: `${id}-form-item-description`,\r\n    formMessageId: `${id}-form-item-message`,\r\n    ...fieldState,\r\n  }\r\n}\r\n\r\ntype FormItemContextValue = {\r\n  id: string\r\n}\r\n\r\nconst FormItemContext = React.createContext<FormItemContextValue>(\r\n  {} as FormItemContextValue\r\n)\r\n\r\nconst FormItem = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement>\r\n>(({ className, ...props }, ref) => {\r\n  const id = React.useId()\r\n\r\n  return (\r\n    <FormItemContext.Provider value={{ id }}>\r\n      <div ref={ref} className={cn(\"space-y-2\", className)} {...props} />\r\n    </FormItemContext.Provider>\r\n  )\r\n})\r\nFormItem.displayName = \"FormItem\"\r\n\r\nconst FormLabel = React.forwardRef<\r\n  React.ElementRef<typeof LabelPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof LabelPrimitive.Root>\r\n>(({ className, ...props }, ref) => {\r\n  const { error, formItemId } = useFormField()\r\n\r\n  return (\r\n    <Label\r\n      ref={ref}\r\n      className={cn(error && \"text-destructive\", className)}\r\n      htmlFor={formItemId}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormLabel.displayName = \"FormLabel\"\r\n\r\nconst FormControl = React.forwardRef<\r\n  React.ElementRef<typeof Slot>,\r\n  React.ComponentPropsWithoutRef<typeof Slot>\r\n>(({ ...props }, ref) => {\r\n  const { error, formItemId, formDescriptionId, formMessageId } = useFormField()\r\n\r\n  return (\r\n    <Slot\r\n      ref={ref}\r\n      id={formItemId}\r\n      aria-describedby={\r\n        !error\r\n          ? `${formDescriptionId}`\r\n          : `${formDescriptionId} ${formMessageId}`\r\n      }\r\n      aria-invalid={!!error}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormControl.displayName = \"FormControl\"\r\n\r\nconst FormDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => {\r\n  const { formDescriptionId } = useFormField()\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formDescriptionId}\r\n      className={cn(\"text-sm text-muted-foreground\", className)}\r\n      {...props}\r\n    />\r\n  )\r\n})\r\nFormDescription.displayName = \"FormDescription\"\r\n\r\nconst FormMessage = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, children, ...props }, ref) => {\r\n  const { error, formMessageId } = useFormField()\r\n  const body = error ? String(error?.message ?? \"\") : children\r\n\r\n  if (!body) {\r\n    return null\r\n  }\r\n\r\n  return (\r\n    <p\r\n      ref={ref}\r\n      id={formMessageId}\r\n      className={cn(\"text-sm font-medium text-destructive\", className)}\r\n      {...props}\r\n    >\r\n      {body}\r\n    </p>\r\n  )\r\n})\r\nFormMessage.displayName = \"FormMessage\"\r\n\r\nexport {\r\n  useFormField,\r\n  Form,\r\n  FormItem,\r\n  FormLabel,\r\n  FormControl,\r\n  FormDescription,\r\n  FormMessage,\r\n  FormField,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;;AAEA;AAEA;AACA;AASA;AACA;AAfA;;;;;;;AAiBA,MAAM,OAAO,8JAAA,CAAA,eAAY;AASzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACzC,CAAC;AAGH,MAAM,YAAY,CAGhB,EACA,GAAG,OACkC;IACrC,qBACE,8OAAC,iBAAiB,QAAQ;QAAC,OAAO;YAAE,MAAM,MAAM,IAAI;QAAC;kBACnD,cAAA,8OAAC,8JAAA,CAAA,aAAU;YAAE,GAAG,KAAK;;;;;;;;;;;AAG3B;AAEA,MAAM,eAAe;IACnB,MAAM,eAAe,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACtC,MAAM,cAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAAE;IACrC,MAAM,EAAE,aAAa,EAAE,SAAS,EAAE,GAAG,CAAA,GAAA,8JAAA,CAAA,iBAAc,AAAD;IAElD,MAAM,aAAa,cAAc,aAAa,IAAI,EAAE;IAEpD,IAAI,CAAC,cAAc;QACjB,MAAM,IAAI,MAAM;IAClB;IAEA,MAAM,EAAE,EAAE,EAAE,GAAG;IAEf,OAAO;QACL;QACA,MAAM,aAAa,IAAI;QACvB,YAAY,GAAG,GAAG,UAAU,CAAC;QAC7B,mBAAmB,GAAG,GAAG,sBAAsB,CAAC;QAChD,eAAe,GAAG,GAAG,kBAAkB,CAAC;QACxC,GAAG,UAAU;IACf;AACF;AAMA,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,gBAAmB,AAAD,EACxC,CAAC;AAGH,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,KAAK,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAErB,qBACE,8OAAC,gBAAgB,QAAQ;QAAC,OAAO;YAAE;QAAG;kBACpC,cAAA,8OAAC;YAAI,KAAK;YAAK,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;YAAa,GAAG,KAAK;;;;;;;;;;;AAGrE;AACA,SAAS,WAAW,GAAG;AAEvB,MAAM,0BAAY,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG/B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,GAAG;IAE9B,qBACE,8OAAC,iIAAA,CAAA,QAAK;QACJ,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,SAAS,oBAAoB;QAC3C,SAAS;QACR,GAAG,KAAK;;;;;;AAGf;AACA,UAAU,WAAW,GAAG;AAExB,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,GAAG,OAAO,EAAE;IACf,MAAM,EAAE,KAAK,EAAE,UAAU,EAAE,iBAAiB,EAAE,aAAa,EAAE,GAAG;IAEhE,qBACE,8OAAC,gKAAA,CAAA,OAAI;QACH,KAAK;QACL,IAAI;QACJ,oBACE,CAAC,QACG,GAAG,mBAAmB,GACtB,GAAG,kBAAkB,CAAC,EAAE,eAAe;QAE7C,gBAAc,CAAC,CAAC;QACf,GAAG,KAAK;;;;;;AAGf;AACA,YAAY,WAAW,GAAG;AAE1B,MAAM,gCAAkB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGrC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE;IAC1B,MAAM,EAAE,iBAAiB,EAAE,GAAG;IAE9B,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AACA,gBAAgB,WAAW,GAAG;AAE9B,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGjC,CAAC,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IACpC,MAAM,EAAE,KAAK,EAAE,aAAa,EAAE,GAAG;IACjC,MAAM,OAAO,QAAQ,OAAO,OAAO,WAAW,MAAM;IAEpD,IAAI,CAAC,MAAM;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,KAAK;QACL,IAAI;QACJ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wCAAwC;QACrD,GAAG,KAAK;kBAER;;;;;;AAGP;AACA,YAAY,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 805, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/alert.tsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { cva, type VariantProps } from \"class-variance-authority\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst alertVariants = cva(\r\n  \"relative w-full rounded-lg border p-4 [&>svg~*]:pl-7 [&>svg+div]:translate-y-[-3px] [&>svg]:absolute [&>svg]:left-4 [&>svg]:top-4 [&>svg]:text-foreground\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default: \"bg-background text-foreground\",\r\n        destructive:\r\n          \"border-destructive/50 text-destructive dark:border-destructive [&>svg]:text-destructive\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nconst Alert = React.forwardRef<\r\n  HTMLDivElement,\r\n  React.HTMLAttributes<HTMLDivElement> & VariantProps<typeof alertVariants>\r\n>(({ className, variant, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    role=\"alert\"\r\n    className={cn(alertVariants({ variant }), className)}\r\n    {...props}\r\n  />\r\n))\r\nAlert.displayName = \"Alert\"\r\n\r\nconst AlertTitle = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLHeadingElement>\r\n>(({ className, ...props }, ref) => (\r\n  <h5\r\n    ref={ref}\r\n    className={cn(\"mb-1 font-medium leading-none tracking-tight\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertTitle.displayName = \"AlertTitle\"\r\n\r\nconst AlertDescription = React.forwardRef<\r\n  HTMLParagraphElement,\r\n  React.HTMLAttributes<HTMLParagraphElement>\r\n>(({ className, ...props }, ref) => (\r\n  <div\r\n    ref={ref}\r\n    className={cn(\"text-sm [&_p]:leading-relaxed\", className)}\r\n    {...props}\r\n  />\r\n))\r\nAlertDescription.displayName = \"AlertDescription\"\r\n\r\nexport { Alert, AlertTitle, AlertDescription }\r\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,6JACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,MAAM,sBAAQ,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG3B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG,OAAO,EAAE,oBACnC,8OAAC;QACC,KAAK;QACL,MAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGb,MAAM,WAAW,GAAG;AAEpB,MAAM,2BAAa,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGhC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,gDAAgD;QAC7D,GAAG,KAAK;;;;;;AAGb,WAAW,WAAW,GAAG;AAEzB,MAAM,iCAAmB,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAGtC,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC;QACC,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGb,iBAAiB,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 869, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/checkbox.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as CheckboxPrimitive from \"@radix-ui/react-checkbox\"\r\nimport { Check } from \"lucide-react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Checkbox = React.forwardRef<\r\n  React.ElementRef<typeof CheckboxPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof CheckboxPrimitive.Root>\r\n>(({ className, ...props }, ref) => (\r\n  <CheckboxPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"peer h-4 w-4 shrink-0 rounded-sm border border-primary ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-primary data-[state=checked]:text-primary-foreground\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <CheckboxPrimitive.Indicator\r\n      className={cn(\"flex items-center justify-center text-current\")}\r\n    >\r\n      <Check className=\"h-4 w-4\" />\r\n    </CheckboxPrimitive.Indicator>\r\n  </CheckboxPrimitive.Root>\r\n))\r\nCheckbox.displayName = CheckboxPrimitive.Root.displayName\r\n\r\nexport { Checkbox }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AANA;;;;;;AAQA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,GAAG,OAAO,EAAE,oBAC1B,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kTACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE;sBAEd,cAAA,8OAAC,oMAAA,CAAA,QAAK;gBAAC,WAAU;;;;;;;;;;;;;;;;AAIvB,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 914, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/components/ui/progress.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport * as React from \"react\"\r\nimport * as ProgressPrimitive from \"@radix-ui/react-progress\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst Progress = React.forwardRef<\r\n  React.ElementRef<typeof ProgressPrimitive.Root>,\r\n  React.ComponentPropsWithoutRef<typeof ProgressPrimitive.Root>\r\n>(({ className, value, ...props }, ref) => (\r\n  <ProgressPrimitive.Root\r\n    ref={ref}\r\n    className={cn(\r\n      \"relative h-4 w-full overflow-hidden rounded-full bg-secondary\",\r\n      className\r\n    )}\r\n    {...props}\r\n  >\r\n    <ProgressPrimitive.Indicator\r\n      className=\"h-full w-full flex-1 bg-primary transition-all\"\r\n      style={{ transform: `translateX(-${100 - (value || 0)}%)` }}\r\n    />\r\n  </ProgressPrimitive.Root>\r\n))\r\nProgress.displayName = ProgressPrimitive.Root.displayName\r\n\r\nexport { Progress }\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAEA;AALA;;;;;AAOA,MAAM,yBAAW,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAG9B,CAAC,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,OAAO,EAAE,oBACjC,8OAAC,oKAAA,CAAA,OAAsB;QACrB,KAAK;QACL,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iEACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oKAAA,CAAA,YAA2B;YAC1B,WAAU;YACV,OAAO;gBAAE,WAAW,CAAC,YAAY,EAAE,MAAM,CAAC,SAAS,CAAC,EAAE,EAAE,CAAC;YAAC;;;;;;;;;;;AAIhE,SAAS,WAAW,GAAG,oKAAA,CAAA,OAAsB,CAAC,WAAW", "debugId": null}}, {"offset": {"line": 953, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/GitHub/FireFrame/FireFrameShare/src/app/signup/page.tsx"], "sourcesContent": ["\"use client\";\r\n\r\nimport React, { useState } from \"react\";\r\nimport Link from \"next/link\";\r\nimport { useRouter } from \"next/navigation\";\r\nimport { useForm } from \"react-hook-form\";\r\nimport { z } from \"zod\";\r\nimport { zodResolver } from \"@hookform/resolvers/zod\";\r\nimport { <PERSON><PERSON> } from \"@/components/ui/button\";\r\nimport {\r\n  Card,\r\n  CardContent,\r\n  CardDescription,\r\n  CardFooter,\r\n  CardHeader,\r\n  CardTitle,\r\n} from \"@/components/ui/card\";\r\nimport { Input } from \"@/components/ui/input\";\r\nimport { Label } from \"@/components/ui/label\";\r\nimport { useAuth } from \"@/hooks/use-auth\";\r\nimport { useToast } from \"@/hooks/use-toast\";\r\nimport {\r\n  Form,\r\n  FormField,\r\n  FormItem,\r\n  FormControl,\r\n  FormMessage,\r\n} from \"@/components/ui/form\";\r\nimport { Alert, AlertDescription } from \"@/components/ui/alert\";\r\nimport { Checkbox } from \"@/components/ui/checkbox\";\r\nimport { Progress } from \"@/components/ui/progress\";\r\nimport { Eye, EyeOff, CheckCircle, XCircle, AlertCircle } from \"lucide-react\";\r\n\r\n// Password strength checker\r\nconst checkPasswordStrength = (password: string) => {\r\n  const checks = {\r\n    length: password.length >= 8,\r\n    uppercase: /[A-Z]/.test(password),\r\n    lowercase: /[a-z]/.test(password),\r\n    number: /\\d/.test(password),\r\n    special: /[!@#$%^&*(),.?\":{}|<>]/.test(password),\r\n  };\r\n\r\n  const score = Object.values(checks).filter(Boolean).length;\r\n  return { checks, score };\r\n};\r\n\r\nconst signupSchema = z\r\n  .object({\r\n    username: z\r\n      .string()\r\n      .min(3, { message: \"Username must be at least 3 characters.\" })\r\n      .max(30, { message: \"Username must be less than 30 characters.\" })\r\n      .regex(/^[a-zA-Z0-9_]+$/, {\r\n        message: \"Username can only contain letters, numbers, and underscores.\",\r\n      }),\r\n    email: z\r\n      .string()\r\n      .email({ message: \"Please enter a valid email address.\" })\r\n      .min(1, { message: \"Email is required.\" }),\r\n    password: z\r\n      .string()\r\n      .min(8, { message: \"Password must be at least 8 characters.\" })\r\n      .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[!@#$%^&*(),.?\":{}|<>])/, {\r\n        message:\r\n          \"Password must contain uppercase, lowercase, number, and special character.\",\r\n      }),\r\n    confirmPassword: z.string(),\r\n    acceptTerms: z.boolean().refine((val) => val === true, {\r\n      message: \"You must accept the terms and conditions.\",\r\n    }),\r\n    acceptPrivacy: z.boolean().refine((val) => val === true, {\r\n      message: \"You must accept the privacy policy.\",\r\n    }),\r\n  })\r\n  .refine((data) => data.password === data.confirmPassword, {\r\n    message: \"Passwords don't match\",\r\n    path: [\"confirmPassword\"],\r\n  });\r\n\r\nexport default function SignupPage() {\r\n  const router = useRouter();\r\n  const { signUp, isLoading, error } = useAuth();\r\n  const { toast } = useToast();\r\n  const [showPassword, setShowPassword] = useState(false);\r\n  const [showConfirmPassword, setShowConfirmPassword] = useState(false);\r\n  const [passwordStrength, setPasswordStrength] = useState({\r\n    checks: {},\r\n    score: 0,\r\n  });\r\n\r\n  const form = useForm<z.infer<typeof signupSchema>>({\r\n    resolver: zodResolver(signupSchema),\r\n    defaultValues: {\r\n      username: \"\",\r\n      email: \"\",\r\n      password: \"\",\r\n      confirmPassword: \"\",\r\n      acceptTerms: false,\r\n      acceptPrivacy: false,\r\n    },\r\n  });\r\n\r\n  const watchPassword = form.watch(\"password\");\r\n\r\n  React.useEffect(() => {\r\n    if (watchPassword) {\r\n      setPasswordStrength(checkPasswordStrength(watchPassword));\r\n    }\r\n  }, [watchPassword]);\r\n\r\n  const onSubmit = async (values: z.infer<typeof signupSchema>) => {\r\n    const { error } = await signUp(\r\n      values.email,\r\n      values.password,\r\n      values.username\r\n    );\r\n\r\n    if (error) {\r\n      toast({\r\n        title: \"Signup Failed\",\r\n        description: error.message,\r\n        variant: \"destructive\",\r\n      });\r\n      return;\r\n    }\r\n\r\n    toast({\r\n      title: \"Account Created Successfully!\",\r\n      description: \"Please check your email to verify your account.\",\r\n    });\r\n\r\n    router.push(\"/login?message=check_email\");\r\n  };\r\n\r\n  return (\r\n    <main className=\"flex items-center justify-center min-h-screen bg-background p-4\">\r\n      <div className=\"w-full max-w-md\">\r\n        <Form {...form}>\r\n          <form onSubmit={form.handleSubmit(onSubmit)}>\r\n            <Card className=\"shadow-2xl\">\r\n              <CardHeader className=\"text-center\">\r\n                <CardTitle className=\"text-3xl font-bold tracking-tight bg-clip-text text-transparent bg-gradient-to-r from-primary to-accent\">\r\n                  FireFrame\r\n                </CardTitle>\r\n                <CardDescription>\r\n                  Create your account to join the community\r\n                </CardDescription>\r\n              </CardHeader>\r\n              <CardContent className=\"space-y-4\">\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"username\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"username\">Username</Label>\r\n                      <FormControl>\r\n                        <Input\r\n                          id=\"username\"\r\n                          placeholder=\"your_username\"\r\n                          {...field}\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"email\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"email\">Email</Label>\r\n                      <FormControl>\r\n                        <Input\r\n                          id=\"email\"\r\n                          placeholder=\"<EMAIL>\"\r\n                          {...field}\r\n                        />\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"password\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"password\">Password</Label>\r\n                      <FormControl>\r\n                        <div className=\"relative\">\r\n                          <Input\r\n                            id=\"password\"\r\n                            type={showPassword ? \"text\" : \"password\"}\r\n                            placeholder=\"••••••••\"\r\n                            autoComplete=\"new-password\"\r\n                            {...field}\r\n                          />\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\r\n                            onClick={() => setShowPassword(!showPassword)}\r\n                          >\r\n                            {showPassword ? (\r\n                              <EyeOff className=\"h-4 w-4\" />\r\n                            ) : (\r\n                              <Eye className=\"h-4 w-4\" />\r\n                            )}\r\n                          </Button>\r\n                        </div>\r\n                      </FormControl>\r\n\r\n                      {watchPassword && (\r\n                        <div className=\"space-y-2\">\r\n                          <div className=\"flex items-center justify-between text-sm\">\r\n                            <span>Password strength:</span>\r\n                            <span\r\n                              className={`font-medium ${\r\n                                passwordStrength.score < 2\r\n                                  ? \"text-red-500\"\r\n                                  : passwordStrength.score < 4\r\n                                  ? \"text-yellow-500\"\r\n                                  : \"text-green-500\"\r\n                              }`}\r\n                            >\r\n                              {passwordStrength.score < 2\r\n                                ? \"Weak\"\r\n                                : passwordStrength.score < 4\r\n                                ? \"Medium\"\r\n                                : \"Strong\"}\r\n                            </span>\r\n                          </div>\r\n                          <Progress\r\n                            value={(passwordStrength.score / 5) * 100}\r\n                            className=\"h-2\"\r\n                          />\r\n                          <div className=\"grid grid-cols-2 gap-1 text-xs\">\r\n                            {Object.entries(passwordStrength.checks).map(\r\n                              ([key, passed]) => (\r\n                                <div\r\n                                  key={key}\r\n                                  className=\"flex items-center gap-1\"\r\n                                >\r\n                                  {passed ? (\r\n                                    <CheckCircle className=\"h-3 w-3 text-green-500\" />\r\n                                  ) : (\r\n                                    <XCircle className=\"h-3 w-3 text-red-500\" />\r\n                                  )}\r\n                                  <span\r\n                                    className={\r\n                                      passed ? \"text-green-600\" : \"text-red-600\"\r\n                                    }\r\n                                  >\r\n                                    {key === \"length\" && \"8+ chars\"}\r\n                                    {key === \"uppercase\" && \"Uppercase\"}\r\n                                    {key === \"lowercase\" && \"Lowercase\"}\r\n                                    {key === \"number\" && \"Number\"}\r\n                                    {key === \"special\" && \"Special\"}\r\n                                  </span>\r\n                                </div>\r\n                              )\r\n                            )}\r\n                          </div>\r\n                        </div>\r\n                      )}\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <FormField\r\n                  control={form.control}\r\n                  name=\"confirmPassword\"\r\n                  render={({ field }) => (\r\n                    <FormItem>\r\n                      <Label htmlFor=\"confirmPassword\">Confirm Password</Label>\r\n                      <FormControl>\r\n                        <div className=\"relative\">\r\n                          <Input\r\n                            id=\"confirmPassword\"\r\n                            type={showConfirmPassword ? \"text\" : \"password\"}\r\n                            placeholder=\"••••••••\"\r\n                            autoComplete=\"new-password\"\r\n                            {...field}\r\n                          />\r\n                          <Button\r\n                            type=\"button\"\r\n                            variant=\"ghost\"\r\n                            size=\"sm\"\r\n                            className=\"absolute right-0 top-0 h-full px-3 py-2 hover:bg-transparent\"\r\n                            onClick={() =>\r\n                              setShowConfirmPassword(!showConfirmPassword)\r\n                            }\r\n                          >\r\n                            {showConfirmPassword ? (\r\n                              <EyeOff className=\"h-4 w-4\" />\r\n                            ) : (\r\n                              <Eye className=\"h-4 w-4\" />\r\n                            )}\r\n                          </Button>\r\n                        </div>\r\n                      </FormControl>\r\n                      <FormMessage />\r\n                    </FormItem>\r\n                  )}\r\n                />\r\n\r\n                <div className=\"space-y-3\">\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"acceptTerms\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                        <div className=\"space-y-1 leading-none\">\r\n                          <Label className=\"text-sm\">\r\n                            I accept the{\" \"}\r\n                            <Link\r\n                              href=\"/terms\"\r\n                              className=\"text-primary hover:underline\"\r\n                            >\r\n                              Terms and Conditions\r\n                            </Link>\r\n                          </Label>\r\n                          <FormMessage />\r\n                        </div>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n\r\n                  <FormField\r\n                    control={form.control}\r\n                    name=\"acceptPrivacy\"\r\n                    render={({ field }) => (\r\n                      <FormItem className=\"flex flex-row items-start space-x-3 space-y-0\">\r\n                        <FormControl>\r\n                          <Checkbox\r\n                            checked={field.value}\r\n                            onCheckedChange={field.onChange}\r\n                          />\r\n                        </FormControl>\r\n                        <div className=\"space-y-1 leading-none\">\r\n                          <Label className=\"text-sm\">\r\n                            I accept the{\" \"}\r\n                            <Link\r\n                              href=\"/privacy\"\r\n                              className=\"text-primary hover:underline\"\r\n                            >\r\n                              Privacy Policy\r\n                            </Link>\r\n                          </Label>\r\n                          <FormMessage />\r\n                        </div>\r\n                      </FormItem>\r\n                    )}\r\n                  />\r\n                </div>\r\n              </CardContent>\r\n              <CardFooter className=\"flex flex-col gap-4\">\r\n                {error && (\r\n                  <Alert variant=\"destructive\">\r\n                    <AlertCircle className=\"h-4 w-4\" />\r\n                    <AlertDescription>{error}</AlertDescription>\r\n                  </Alert>\r\n                )}\r\n\r\n                <Button\r\n                  type=\"submit\"\r\n                  className=\"w-full font-bold\"\r\n                  disabled={isLoading}\r\n                >\r\n                  {isLoading ? \"Creating Account...\" : \"Sign Up\"}\r\n                </Button>\r\n                <p className=\"text-sm text-center text-muted-foreground\">\r\n                  Already have an account?{\" \"}\r\n                  <Link\r\n                    href=\"/login\"\r\n                    className=\"font-semibold text-primary hover:underline\"\r\n                  >\r\n                    Sign in\r\n                  </Link>\r\n                </p>\r\n              </CardFooter>\r\n            </Card>\r\n          </form>\r\n        </Form>\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AACA;AACA;AACA;AACA;AAOA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AA/BA;;;;;;;;;;;;;;;;;;;AAiCA,4BAA4B;AAC5B,MAAM,wBAAwB,CAAC;IAC7B,MAAM,SAAS;QACb,QAAQ,SAAS,MAAM,IAAI;QAC3B,WAAW,QAAQ,IAAI,CAAC;QACxB,WAAW,QAAQ,IAAI,CAAC;QACxB,QAAQ,KAAK,IAAI,CAAC;QAClB,SAAS,yBAAyB,IAAI,CAAC;IACzC;IAEA,MAAM,QAAQ,OAAO,MAAM,CAAC,QAAQ,MAAM,CAAC,SAAS,MAAM;IAC1D,OAAO;QAAE;QAAQ;IAAM;AACzB;AAEA,MAAM,eAAe,oIAAA,CAAA,IAAC,CACnB,MAAM,CAAC;IACN,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA0C,GAC5D,GAAG,CAAC,IAAI;QAAE,SAAS;IAA4C,GAC/D,KAAK,CAAC,mBAAmB;QACxB,SAAS;IACX;IACF,OAAO,oIAAA,CAAA,IAAC,CACL,MAAM,GACN,KAAK,CAAC;QAAE,SAAS;IAAsC,GACvD,GAAG,CAAC,GAAG;QAAE,SAAS;IAAqB;IAC1C,UAAU,oIAAA,CAAA,IAAC,CACR,MAAM,GACN,GAAG,CAAC,GAAG;QAAE,SAAS;IAA0C,GAC5D,KAAK,CAAC,+DAA+D;QACpE,SACE;IACJ;IACF,iBAAiB,oIAAA,CAAA,IAAC,CAAC,MAAM;IACzB,aAAa,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;QACrD,SAAS;IACX;IACA,eAAe,oIAAA,CAAA,IAAC,CAAC,OAAO,GAAG,MAAM,CAAC,CAAC,MAAQ,QAAQ,MAAM;QACvD,SAAS;IACX;AACF,GACC,MAAM,CAAC,CAAC,OAAS,KAAK,QAAQ,KAAK,KAAK,eAAe,EAAE;IACxD,SAAS;IACT,MAAM;QAAC;KAAkB;AAC3B;AAEa,SAAS;IACtB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,MAAM,EAAE,SAAS,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,2HAAA,CAAA,UAAO,AAAD;IAC3C,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,4HAAA,CAAA,WAAQ,AAAD;IACzB,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACjD,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;QACvD,QAAQ,CAAC;QACT,OAAO;IACT;IAEA,MAAM,OAAO,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAgC;QACjD,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,UAAU;YACV,OAAO;YACP,UAAU;YACV,iBAAiB;YACjB,aAAa;YACb,eAAe;QACjB;IACF;IAEA,MAAM,gBAAgB,KAAK,KAAK,CAAC;IAEjC,qMAAA,CAAA,UAAK,CAAC,SAAS,CAAC;QACd,IAAI,eAAe;YACjB,oBAAoB,sBAAsB;QAC5C;IACF,GAAG;QAAC;KAAc;IAElB,MAAM,WAAW,OAAO;QACtB,MAAM,EAAE,KAAK,EAAE,GAAG,MAAM,OACtB,OAAO,KAAK,EACZ,OAAO,QAAQ,EACf,OAAO,QAAQ;QAGjB,IAAI,OAAO;YACT,MAAM;gBACJ,OAAO;gBACP,aAAa,MAAM,OAAO;gBAC1B,SAAS;YACX;YACA;QACF;QAEA,MAAM;YACJ,OAAO;YACP,aAAa;QACf;QAEA,OAAO,IAAI,CAAC;IACd;IAEA,qBACE,8OAAC;QAAK,WAAU;kBACd,cAAA,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC,gIAAA,CAAA,OAAI;gBAAE,GAAG,IAAI;0BACZ,cAAA,8OAAC;oBAAK,UAAU,KAAK,YAAY,CAAC;8BAChC,cAAA,8OAAC,gIAAA,CAAA,OAAI;wBAAC,WAAU;;0CACd,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;kDACpB,8OAAC,gIAAA,CAAA,YAAS;wCAAC,WAAU;kDAA0G;;;;;;kDAG/H,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;gCAAC,WAAU;;kDACrB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAIlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAQ;;;;;;kEACvB,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC,iIAAA,CAAA,QAAK;4DACJ,IAAG;4DACH,aAAY;4DACX,GAAG,KAAK;;;;;;;;;;;kEAGb,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAIlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAW;;;;;;kEAC1B,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAM,eAAe,SAAS;oEAC9B,aAAY;oEACZ,cAAa;oEACZ,GAAG,KAAK;;;;;;8EAEX,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IAAM,gBAAgB,CAAC;8EAE/B,6BACC,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;+FAElB,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;oDAMtB,+BACC,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;;kFACb,8OAAC;kFAAK;;;;;;kFACN,8OAAC;wEACC,WAAW,CAAC,YAAY,EACtB,iBAAiB,KAAK,GAAG,IACrB,iBACA,iBAAiB,KAAK,GAAG,IACzB,oBACA,kBACJ;kFAED,iBAAiB,KAAK,GAAG,IACtB,SACA,iBAAiB,KAAK,GAAG,IACzB,WACA;;;;;;;;;;;;0EAGR,8OAAC,oIAAA,CAAA,WAAQ;gEACP,OAAO,AAAC,iBAAiB,KAAK,GAAG,IAAK;gEACtC,WAAU;;;;;;0EAEZ,8OAAC;gEAAI,WAAU;0EACZ,OAAO,OAAO,CAAC,iBAAiB,MAAM,EAAE,GAAG,CAC1C,CAAC,CAAC,KAAK,OAAO,iBACZ,8OAAC;wEAEC,WAAU;;4EAET,uBACC,8OAAC,2NAAA,CAAA,cAAW;gFAAC,WAAU;;;;;uGAEvB,8OAAC,4MAAA,CAAA,UAAO;gFAAC,WAAU;;;;;;0FAErB,8OAAC;gFACC,WACE,SAAS,mBAAmB;;oFAG7B,QAAQ,YAAY;oFACpB,QAAQ,eAAe;oFACvB,QAAQ,eAAe;oFACvB,QAAQ,YAAY;oFACpB,QAAQ,aAAa;;;;;;;;uEAjBnB;;;;;;;;;;;;;;;;kEAyBjB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC,gIAAA,CAAA,YAAS;wCACR,SAAS,KAAK,OAAO;wCACrB,MAAK;wCACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;;kEACP,8OAAC,iIAAA,CAAA,QAAK;wDAAC,SAAQ;kEAAkB;;;;;;kEACjC,8OAAC,gIAAA,CAAA,cAAW;kEACV,cAAA,8OAAC;4DAAI,WAAU;;8EACb,8OAAC,iIAAA,CAAA,QAAK;oEACJ,IAAG;oEACH,MAAM,sBAAsB,SAAS;oEACrC,aAAY;oEACZ,cAAa;oEACZ,GAAG,KAAK;;;;;;8EAEX,8OAAC,kIAAA,CAAA,SAAM;oEACL,MAAK;oEACL,SAAQ;oEACR,MAAK;oEACL,WAAU;oEACV,SAAS,IACP,uBAAuB,CAAC;8EAGzB,oCACC,8OAAC,0MAAA,CAAA,SAAM;wEAAC,WAAU;;;;;+FAElB,8OAAC,gMAAA,CAAA,MAAG;wEAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;kEAKvB,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;kDAKlB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;0EAGnC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;;4EAAU;4EACZ;0FACb,8OAAC,4JAAA,CAAA,UAAI;gFACH,MAAK;gFACL,WAAU;0FACX;;;;;;;;;;;;kFAIH,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;0DAMpB,8OAAC,gIAAA,CAAA,YAAS;gDACR,SAAS,KAAK,OAAO;gDACrB,MAAK;gDACL,QAAQ,CAAC,EAAE,KAAK,EAAE,iBAChB,8OAAC,gIAAA,CAAA,WAAQ;wDAAC,WAAU;;0EAClB,8OAAC,gIAAA,CAAA,cAAW;0EACV,cAAA,8OAAC,oIAAA,CAAA,WAAQ;oEACP,SAAS,MAAM,KAAK;oEACpB,iBAAiB,MAAM,QAAQ;;;;;;;;;;;0EAGnC,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,iIAAA,CAAA,QAAK;wEAAC,WAAU;;4EAAU;4EACZ;0FACb,8OAAC,4JAAA,CAAA,UAAI;gFACH,MAAK;gFACL,WAAU;0FACX;;;;;;;;;;;;kFAIH,8OAAC,gIAAA,CAAA,cAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0CAOxB,8OAAC,gIAAA,CAAA,aAAU;gCAAC,WAAU;;oCACnB,uBACC,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;;0DACb,8OAAC,oNAAA,CAAA,cAAW;gDAAC,WAAU;;;;;;0DACvB,8OAAC,iIAAA,CAAA,mBAAgB;0DAAE;;;;;;;;;;;;kDAIvB,8OAAC,kIAAA,CAAA,SAAM;wCACL,MAAK;wCACL,WAAU;wCACV,UAAU;kDAET,YAAY,wBAAwB;;;;;;kDAEvC,8OAAC;wCAAE,WAAU;;4CAA4C;4CAC9B;0DACzB,8OAAC,4JAAA,CAAA,UAAI;gDACH,MAAK;gDACL,WAAU;0DACX;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWnB", "debugId": null}}]}